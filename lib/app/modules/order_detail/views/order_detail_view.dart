import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:omni_datetime_picker/omni_datetime_picker.dart';
import 'package:flutter_map/flutter_map.dart';
import 'package:latlong2/latlong.dart';

import '../../../../colors.dart';
import '../../../../extension.dart';
import '../../../models/erp_category.dart';
import '../../../routes/app_pages.dart';
import '../controllers/order_detail_controller.dart';

class OrderDetailView extends GetView<OrderDetailController> {
  const OrderDetailView({super.key});

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Scaffold(
      backgroundColor: isDark ? ErpColors.darkBackground : ErpColors.background,
      body: Column(
        children: [
          // 主要內容區域
          Expanded(
            child: SingleChildScrollView(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: _buildBody(context).toList(growable: false),
              ),
            ),
          ),
          // 底部操作按鈕
          _buildBottomActions(context),
        ],
      ),
    );
  }

  Iterable<Widget> _buildBody(BuildContext context) sync* {
    yield _buildTransactionTypeSelector(context);
    yield const SizedBox(height: 24);
    yield _buildAmountSection(context);
    yield const SizedBox(height: 8);
    yield _buildNumberKeypad(context);
    yield _buildCategoryAndDateTimeRow(context);
    yield const SizedBox(height: 24);
    yield _buildNoteInput(context);
    yield const SizedBox(height: 24);
    yield _buildLocationSelector(context);
  }

  // 交易類型選擇器
  Widget _buildTransactionTypeSelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '交易類型',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: isDark ? ErpColors.darkSurface : const Color(0xFFF3F4F6),
            borderRadius: BorderRadius.circular(8),
          ),
          padding: const EdgeInsets.all(4),
          child: Obx(() => Row(
                children: TransactionType.values.map((type) {
                  final isSelected =
                      controller.selectedTransactionType.value == type;
                  return Expanded(
                    child: GestureDetector(
                      onTap: () => controller.setTransactionType(type),
                      child: Container(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                        decoration: BoxDecoration(
                          color: isSelected
                              ? (isDark
                                  ? ErpColors.darkCardBackground
                                  : Colors.white)
                              : Colors.transparent,
                          borderRadius: BorderRadius.circular(6),
                          boxShadow: isSelected
                              ? [
                                  BoxShadow(
                                    color: Colors.black.withOpacity(0.1),
                                    blurRadius: 2,
                                    offset: const Offset(0, 1),
                                  ),
                                ]
                              : null,
                        ),
                        child: Row(
                          mainAxisAlignment: MainAxisAlignment.center,
                          children: [
                            Icon(
                              controller.getTransactionTypeIcon(type),
                              size: 16,
                              color: isSelected
                                  ? ErpColors.primary
                                  : (isDark
                                      ? ErpColors.darkTextSecondary
                                      : ErpColors.textSecondary),
                            ),
                            const SizedBox(width: 4),
                            Text(
                              controller.getTransactionTypeDisplayName(type),
                              style: TextStyle(
                                fontSize: 14,
                                fontWeight: FontWeight.w500,
                                color: isSelected
                                    ? ErpColors.primary
                                    : (isDark
                                        ? ErpColors.darkTextSecondary
                                        : ErpColors.textSecondary),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  );
                }).toList(),
              )),
        ),
      ],
    );
  }

  // 金額輸入區域
  Widget _buildAmountSection(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '金額',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          width: double.infinity,
          padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 16),
          decoration: BoxDecoration(
            color: isDark ? ErpColors.darkCardBackground : Colors.white,
            border: Border.all(
              color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Row(
            children: [
              Text(
                '\$',
                style: TextStyle(
                  fontSize: 24,
                  color: isDark
                      ? ErpColors.darkTextSecondary
                      : ErpColors.textSecondary,
                ),
              ),
              const SizedBox(width: 8),
              Expanded(
                child: Obx(() => Text(
                      controller.amount.value,
                      textAlign: TextAlign.center,
                      style: TextStyle(
                        fontSize: 32,
                        fontWeight: FontWeight.bold,
                        color: isDark
                            ? ErpColors.darkTextPrimary
                            : ErpColors.textPrimary,
                      ),
                    )),
              ),
            ],
          ),
        ),
      ],
    );
  }

  // 數字鍵盤
  Widget _buildNumberKeypad(BuildContext context) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 3,
      crossAxisSpacing: 6,
      mainAxisSpacing: 6,
      childAspectRatio: 2.0,
      children: [
        // 第一行
        _buildKeypadButton('1', context),
        _buildKeypadButton('2', context),
        _buildKeypadButton('3', context),
        // 第二行
        _buildKeypadButton('4', context),
        _buildKeypadButton('5', context),
        _buildKeypadButton('6', context),
        // 第三行
        _buildKeypadButton('7', context),
        _buildKeypadButton('8', context),
        _buildKeypadButton('9', context),
        // 第四行
        _buildKeypadButton('.', context),
        _buildKeypadButton('0', context),
        _buildDeleteButton(context),
      ],
    );
  }

  // 鍵盤按鈕
  Widget _buildKeypadButton(String digit, BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Material(
      color: isDark ? ErpColors.darkCardBackground : Colors.white,
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: () => controller.addDigit(digit),
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Text(
              digit,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.w600,
                color:
                    isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
              ),
            ),
          ),
        ),
      ),
    );
  }

  // 刪除按鈕
  Widget _buildDeleteButton(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Material(
      color: isDark ? const Color(0xFF2D1B1B) : const Color(0xFFFEF2F2),
      borderRadius: BorderRadius.circular(12),
      child: InkWell(
        onTap: controller.deleteDigit,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          decoration: BoxDecoration(
            border: Border.all(
              color: isDark ? const Color(0xFF7F1D1D) : const Color(0xFFFECACA),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: Center(
            child: Icon(
              Icons.backspace,
              color: isDark ? const Color(0xFFEF4444) : const Color(0xFFDC2626),
              size: 18,
            ),
          ),
        ),
      ),
    );
  }

  // 分類和日期時間選擇器行
  Widget _buildCategoryAndDateTimeRow(BuildContext context) {
    return Row(
      children: [
        Expanded(child: _buildCategorySelector(context)),
        const SizedBox(width: 12),
        Expanded(child: _buildDateTimeSelector(context)),
      ],
    );
  }

  // 分類選擇器
  Widget _buildCategorySelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '消費類別',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => GestureDetector(
              onTap: () => _showCategoryPicker(context),
              child: Container(
                width: double.infinity,
                padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isDark ? ErpColors.darkCardBackground : Colors.white,
                  border: Border.all(
                    color:
                        isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Row(
                  children: [
                    if (controller.selectedCategory.value != null) ...[
                      Container(
                        width: 20,
                        height: 20,
                        decoration: BoxDecoration(
                          color: controller.selectedCategory.value!
                              .getColor()
                              .withOpacity(0.1),
                          borderRadius: BorderRadius.circular(10),
                        ),
                        child: Icon(
                          controller.selectedCategory.value!.getIcon(),
                          size: 12,
                          color: controller.selectedCategory.value!.getColor(),
                        ),
                      ),
                      const SizedBox(width: 8),
                      Expanded(
                        child: Text(
                          controller.selectedCategory.value!.name ?? '',
                          style: TextStyle(
                            fontSize: 14,
                            color: isDark
                                ? ErpColors.darkTextPrimary
                                : ErpColors.textPrimary,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ] else ...[
                      Expanded(
                        child: Text(
                          '請選擇類別',
                          style: TextStyle(
                            fontSize: 14,
                            color: isDark
                                ? ErpColors.darkTextSecondary
                                : ErpColors.textSecondary,
                          ),
                        ),
                      ),
                    ],
                    Icon(
                      Icons.keyboard_arrow_down,
                      color: isDark
                          ? ErpColors.darkTextSecondary
                          : ErpColors.textSecondary,
                      size: 16,
                    ),
                  ],
                ),
              ),
            )),
      ],
    );
  }

  // 日期時間選擇器
  Widget _buildDateTimeSelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '日期時間',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() => GestureDetector(
              onTap: () => _showDateTimePicker(context),
              child: Container(
                width: double.infinity,
                padding:
                    const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
                decoration: BoxDecoration(
                  color: isDark ? ErpColors.darkCardBackground : Colors.white,
                  border: Border.all(
                    color:
                        isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
                  ),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Text(
                  DateFormat('MM-dd HH:mm')
                      .format(controller.selectedDateTime.value),
                  style: TextStyle(
                    fontSize: 14,
                    color: isDark
                        ? ErpColors.darkTextPrimary
                        : ErpColors.textPrimary,
                  ),
                ),
              ),
            )),
      ],
    );
  }

  // 備註輸入
  Widget _buildNoteInput(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '備註',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Container(
          decoration: BoxDecoration(
            color: isDark ? ErpColors.darkCardBackground : Colors.white,
            border: Border.all(
              color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
            ),
            borderRadius: BorderRadius.circular(12),
          ),
          child: TextField(
            controller: controller.noteController,
            onChanged: controller.setNote,
            maxLines: 3,
            decoration: InputDecoration(
              hintText: '添加備註...',
              hintStyle: TextStyle(
                color: isDark
                    ? ErpColors.darkTextSecondary
                    : ErpColors.textSecondary,
              ),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.all(16),
            ),
            style: TextStyle(
              color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
            ),
          ),
        ),
      ],
    );
  }

  // 地點選擇器
  Widget _buildLocationSelector(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          '地點 (選填)',
          style: TextStyle(
            fontSize: 14,
            fontWeight: FontWeight.w500,
            color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
          ),
        ),
        const SizedBox(height: 8),
        Obx(() {
          // 如果有地理位置資料，顯示地圖
          if (controller.latitude.value != null && controller.longitude.value != null) {
            return _buildLocationMap(context);
          } else {
            // 沒有地理位置資料時，顯示添加地點的按鈕
            return _buildAddLocationButton(context);
          }
        }),
      ],
    );
  }

  // 構建地圖顯示
  Widget _buildLocationMap(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return GestureDetector(
      onTap: () => _showLocationPicker(context),
      child: Container(
        width: double.infinity,
        height: 120,
        decoration: BoxDecoration(
          color: isDark ? ErpColors.darkCardBackground : Colors.white,
          border: Border.all(
            color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: ClipRRect(
          borderRadius: BorderRadius.circular(12),
          child: Stack(
            children: [
              // 地圖
              FlutterMap(
                options: MapOptions(
                  initialCenter: LatLng(
                    controller.latitude.value!,
                    controller.longitude.value!,
                  ),
                  initialZoom: 15.0,
                  interactionOptions: const InteractionOptions(
                    flags: InteractiveFlag.none, // 禁用所有交互
                  ),
                ),
                children: [
                  TileLayer(
                    urlTemplate: 'https://wmts.nlsc.gov.tw/wmts/EMAP/default/GoogleMapsCompatible/{z}/{y}/{x}',
                    userAgentPackageName: 'com.example.pockettrac',
                  ),
                  MarkerLayer(
                    markers: [
                      Marker(
                        point: LatLng(
                          controller.latitude.value!,
                          controller.longitude.value!,
                        ),
                        width: 40,
                        height: 40,
                        child: const Icon(
                          Icons.location_on,
                          color: Colors.red,
                          size: 30,
                        ),
                      ),
                    ],
                  ),
                ],
              ),
              // 覆蓋層，顯示地點資訊
              Positioned(
                bottom: 8,
                left: 8,
                right: 8,
                child: Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: Colors.black.withValues(alpha: 0.7),
                    borderRadius: BorderRadius.circular(6),
                  ),
                  child: Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        color: Colors.white,
                        size: 16,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          controller.location.value.isNotEmpty
                              ? controller.location.value
                              : '${controller.latitude.value!.toStringAsFixed(4)}, ${controller.longitude.value!.toStringAsFixed(4)}',
                          style: const TextStyle(
                            color: Colors.white,
                            fontSize: 12,
                          ),
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      const Icon(
                        Icons.edit,
                        color: Colors.white,
                        size: 16,
                      ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  // 構建添加地點按鈕
  Widget _buildAddLocationButton(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return GestureDetector(
      onTap: () => _showLocationPicker(context),
      child: Container(
        width: double.infinity,
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
        decoration: BoxDecoration(
          color: isDark ? ErpColors.darkCardBackground : Colors.white,
          border: Border.all(
            color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
          ),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          children: [
            Icon(
              Icons.location_on,
              color: isDark
                  ? ErpColors.darkTextSecondary
                  : ErpColors.textSecondary,
              size: 20,
            ),
            const SizedBox(width: 12),
            Expanded(
              child: Text(
                '添加地點',
                style: TextStyle(
                  color: isDark
                      ? ErpColors.darkTextSecondary
                      : ErpColors.textSecondary,
                ),
              ),
            ),
            Icon(
              Icons.chevron_right,
              color: isDark
                  ? ErpColors.darkTextSecondary
                  : ErpColors.textSecondary,
              size: 20,
            ),
          ],
        ),
      ),
    );
  }

  // 底部操作按鈕
  Widget _buildBottomActions(BuildContext context) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: isDark ? ErpColors.darkCardBackground : Colors.white,
        border: Border(
          top: BorderSide(
            color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
          ),
        ),
      ),
      child: SafeArea(
        child: Row(
          children: [
            Expanded(
              child: ElevatedButton(
                onPressed: controller.cancel,
                style: ElevatedButton.styleFrom(
                  backgroundColor:
                      isDark ? ErpColors.darkSurface : const Color(0xFFF3F4F6),
                  foregroundColor: isDark
                      ? ErpColors.darkTextSecondary
                      : ErpColors.textSecondary,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  '取消',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ),
            ),
            const SizedBox(width: 12),
            Expanded(
              child: ElevatedButton(
                onPressed: controller.saveTransaction,
                style: ElevatedButton.styleFrom(
                  backgroundColor: ErpColors.primary,
                  foregroundColor: Colors.white,
                  elevation: 0,
                  padding: const EdgeInsets.symmetric(vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: controller.saveButtonState.obx(
                  (state) {
                    return const Text(
                      '儲存交易',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w500,
                      ),
                    );
                  },
                  onLoading: const SizedBox(
                    height: 20,
                    width: 20,
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                    ),
                  ),
                  onError: (error) => const Text(
                    '重試',
                    style: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 顯示日期時間選擇器
  Future<void> _showDateTimePicker(BuildContext context) async {
    final DateTime? pickedDateTime = await showOmniDateTimePicker(
      context: context,
      initialDate: controller.selectedDateTime.value,
      firstDate: DateTime(2000),
      lastDate: DateTime(2100),
      is24HourMode: true,
      isShowSeconds: false,
      minutesInterval: 1,
      secondsInterval: 1,
      borderRadius: const BorderRadius.all(Radius.circular(16)),
      constraints: const BoxConstraints(
        maxWidth: 350,
        maxHeight: 650,
      ),
      transitionBuilder: (context, anim1, anim2, child) {
        return FadeTransition(
          opacity: anim1.drive(
            Tween(
              begin: 0,
              end: 1,
            ),
          ),
          child: child,
        );
      },
      transitionDuration: const Duration(milliseconds: 200),
      barrierDismissible: true,
      selectableDayPredicate: (dateTime) {
        // 可以在這裡添加日期限制邏輯
        return true;
      },
    );

    if (pickedDateTime != null) {
      controller.setDateTime(pickedDateTime);
    }
  }

  // 顯示分類選擇器彈窗
  Future<void> _showCategoryPicker(BuildContext context) async {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;

    await showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        height: MediaQuery.of(context).size.height * 0.7,
        decoration: BoxDecoration(
          color: isDark ? ErpColors.darkCardBackground : Colors.white,
          borderRadius: const BorderRadius.only(
            topLeft: Radius.circular(20),
            topRight: Radius.circular(20),
          ),
        ),
        child: Column(
          children: [
            // 拖拽指示器
            Container(
              margin: const EdgeInsets.only(top: 8),
              height: 4,
              width: 40,
              decoration: BoxDecoration(
                color: isDark ? ErpColors.darkTextSecondary : Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),
            // 標題欄
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                    color: isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB),
                  ),
                ),
              ),
              child: Row(
                children: [
                  Text(
                    '選擇類別',
                    style: TextStyle(
                      fontSize: 18,
                      fontWeight: FontWeight.w600,
                      color: isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () => Navigator.of(context).pop(),
                    icon: Icon(
                      Icons.close,
                      color: isDark ? ErpColors.darkTextSecondary : ErpColors.textSecondary,
                    ),
                    style: IconButton.styleFrom(
                      padding: EdgeInsets.zero,
                      minimumSize: const Size(24, 24),
                    ),
                  ),
                ],
              ),
            ),
            // 分類網格
            Expanded(
              child: Obx(() => Padding(
                    padding: const EdgeInsets.all(16),
                    child: controller.categories.isEmpty
                        ? Center(
                            child: Text(
                              '暫無分類',
                              style: TextStyle(
                                color: isDark
                                    ? ErpColors.darkTextSecondary
                                    : ErpColors.textSecondary,
                              ),
                            ),
                          )
                        : GridView.builder(
                            gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                              crossAxisCount: 3,
                              crossAxisSpacing: 12,
                              mainAxisSpacing: 12,
                              childAspectRatio: 1.0,
                            ),
                            itemCount: controller.categories.length,
                            itemBuilder: (context, index) {
                              final category = controller.categories[index];
                              return _buildCategoryGridItem(context, category);
                            },
                          ),
                  )),
            ),
          ],
        ),
      ),
    );
  }

  // 構建分類網格項目
  Widget _buildCategoryGridItem(BuildContext context, ErpCategory category) {
    final theme = Theme.of(context);
    final isDark = theme.brightness == Brightness.dark;
    final isSelected = controller.selectedCategory.value?.id == category.id;

    return GestureDetector(
      onTap: () {
        controller.setCategory(category);
        Navigator.of(context).pop();
      },
      child: Container(
        decoration: BoxDecoration(
          color: isSelected
              ? category.getColor().withOpacity(0.1)
              : (isDark ? ErpColors.darkSurface : const Color(0xFFF9FAFB)),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: isSelected
                ? category.getColor()
                : (isDark ? ErpColors.darkBorder : const Color(0xFFE5E7EB)),
            width: isSelected ? 2 : 1,
          ),
        ),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                color: category.getColor().withOpacity(0.2),
                borderRadius: BorderRadius.circular(20),
              ),
              child: Icon(
                category.getIcon(),
                size: 20,
                color: category.getColor(),
              ),
            ),
            const SizedBox(height: 8),
            Text(
              category.name ?? '',
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.w500,
                color: isSelected
                    ? category.getColor()
                    : (isDark ? ErpColors.darkTextPrimary : ErpColors.textPrimary),
              ),
            ),
          ],
        ),
      ),
    );
  }

  // 顯示地點選擇器
  Future<void> _showLocationPicker(BuildContext context) async {
    final result = await Get.toNamed(
      Routes.LOCATION_PICKER,
      arguments: {
        'latitude': controller.latitude.value,
        'longitude': controller.longitude.value,
        'locationName': controller.location.value,
      },
    );

    if (result != null && result is Map<String, dynamic>) {
      final latitude = result['latitude'] as double?;
      final longitude = result['longitude'] as double?;
      final locationName = result['locationName'] as String?;

      controller.setLocationCoordinates(latitude, longitude, locationName);
    }
  }
}
